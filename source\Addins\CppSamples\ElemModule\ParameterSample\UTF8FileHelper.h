﻿#pragma once

#include <string>
#include <fstream>

#ifdef _WIN32
#include <windows.h>
#endif

namespace UTF8FileHelper
{
    // 转换宽字符串为UTF-8字符串
    inline std::string WStringToUTF8(const std::wstring& wstr)
    {
#ifdef _WIN32
        if (wstr.empty()) 
            return std::string();
        
        int len = WideCharToMultiByte(CP_UTF8, 0, wstr.c_str(), -1, nullptr, 0, nullptr, nullptr);
        if (len <= 0) 
            return std::string();
        
        std::string result;
        result.resize(len - 1);
        WideCharToMultiByte(CP_UTF8, 0, wstr.c_str(), -1, const_cast<char*>(result.data()), len, nullptr, nullptr);
        return result;
#else
        // 简化的非Windows版本
        std::string result;
        for (wchar_t wc : wstr) {
            if (wc < 128) {
                result += static_cast<char>(wc);
            } else {
                result += "?"; // 简化处理
            }
        }
        return result;
#endif
    }

    // 保存UTF-8编码的文本文件
    inline bool SaveUTF8File(const std::wstring& filePath, const std::string& content)
    {
        // 转换文件路径
        std::string filePathUtf8 = WStringToUTF8(filePath);
        if (filePathUtf8.empty())
            return false;

        // 打开文件
        std::ofstream outFile(filePathUtf8, std::ios::out | std::ios::binary);
        if (!outFile.is_open())
            return false;

        try
        {
            // 写入UTF-8 BOM
            outFile.write("\xEF\xBB\xBF", 3);
            
            // 写入内容
            outFile.write(content.c_str(), content.length());
            
            outFile.close();
            return true;
        }
        catch (...)
        {
            if (outFile.is_open())
                outFile.close();
            return false;
        }
    }

    // 保存Mermaid图文件的专用函数
    inline bool SaveMermaidGraphFile(const std::wstring& filePath, const std::wstring& mermaidGraph, size_t calculatorCount)
    {
        // 准备内容
        std::string content;
        content += "# 计算器关联更新图\n\n";
        content += "分析了 " + std::to_string(calculatorCount) + " 个计算器\n\n";
        content += "```mermaid\n";
        content += WStringToUTF8(mermaidGraph);
        content += "\n```\n\n";
        content += "## 说明\n\n";
        content += "这是由CmdCalculatorGraphAnalyzer生成的计算器依赖关系图。\n";
        content += "每个节点代表一个计算器，箭头表示数据依赖关系。\n\n";
        content += "### 图例\n";
        content += "- 蓝色节点：墙相关计算器\n";
        content += "- 紫色节点：楼板相关计算器\n";
        content += "- 绿色节点：梁相关计算器\n";
        content += "- 箭头：数据流向和更新依赖\n\n";
        content += "### 使用方法\n";
        content += "1. 复制上面的mermaid代码\n";
        content += "2. 在支持Mermaid的工具中查看（如Typora、VS Code、GitHub等）\n";
        content += "3. 或访问 https://mermaid.live/ 在线查看\n";

        return SaveUTF8File(filePath, content);
    }
}
