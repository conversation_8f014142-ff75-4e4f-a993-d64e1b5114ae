# UTF-8文件保存方案使用说明

## 概述

为了解决CmdCalculatorGraphAnalyzer中文件输出编码问题，我们实现了第一种UTF-8转换方案。这个方案可以确保中文字符正确保存到文件中，并在各种文本编辑器中正常显示。

## 核心文件

### 1. UTF8FileHelper.h
这是一个独立的头文件，包含了UTF-8文件操作的所有功能：

```cpp
#include "UTF8FileHelper.h"

// 使用示例
std::wstring mermaidGraph = L"graph TD\n    calc0[\"墙参数计算器\"]\n";
bool success = UTF8FileHelper::SaveMermaidGraphFile(L"output.md", mermaidGraph, 3);
```

### 2. 主要功能

#### WStringToUTF8函数
```cpp
std::string utf8String = UTF8FileHelper::WStringToUTF8(L"中文字符串");
```
- 将宽字符串转换为UTF-8编码的字符串
- 在Windows上使用`WideCharToMultiByte`API
- 在其他平台上提供简化实现

#### SaveUTF8File函数
```cpp
bool success = UTF8FileHelper::SaveUTF8File(L"file.txt", "UTF-8内容");
```
- 保存UTF-8编码的文本文件
- 自动添加UTF-8 BOM标记
- 使用二进制模式确保编码正确

#### SaveMermaidGraphFile函数
```cpp
bool success = UTF8FileHelper::SaveMermaidGraphFile(filePath, mermaidGraph, calculatorCount);
```
- 专门用于保存Mermaid图文件
- 自动生成完整的Markdown格式
- 包含中文说明和使用指南

## 在CmdCalculatorGraphAnalyzer中的使用

### 1. 包含头文件
```cpp
#include "UTF8FileHelper.h"
```

### 2. 修改文件保存代码
原来的代码：
```cpp
std::wofstream outFile(filePathName);
outFile << L"# 计算器关联更新图\n\n";
// ... 其他内容
```

修改后的代码：
```cpp
// 使用UTF-8转换方式保存文件
if (!filePathName.empty())
{
    bool success = UTF8FileHelper::SaveMermaidGraphFile(filePathName, mermaidGraph, calculatorInfos.size());
    if (success)
    {
        // 文件保存成功
    }
    else
    {
        // 文件保存失败
    }
}
```

## 测试方法

### 1. 编译测试程序
```bash
g++ -o test_utf8_save test_utf8_save.cpp -std=c++11
```

### 2. 运行测试
```bash
./test_utf8_save
```

### 3. 检查输出文件
- `test_calculator_graph.md` - 完整的Mermaid图文件
- `simple_test.txt` - 简单的UTF-8测试文件

### 4. 验证编码
用以下工具打开生成的文件，检查中文是否正确显示：
- Notepad++（查看编码为UTF-8 BOM）
- VS Code
- Typora（查看Mermaid图渲染效果）

## 优势

### 1. 完全支持中文
- 正确处理中文字符编码
- 添加UTF-8 BOM确保编辑器识别
- 兼容各种文本编辑器

### 2. 跨平台兼容
- Windows上使用系统API
- 其他平台提供备用实现
- 统一的接口调用

### 3. 易于使用
- 简单的函数调用
- 自动处理编码转换
- 完整的错误处理

### 4. 功能完整
- 自动生成Markdown格式
- 包含详细的中文说明
- 提供使用指南

## 故障排除

### 1. 编译错误
如果遇到编译错误，检查：
- 是否包含了`windows.h`头文件
- 是否在Windows平台上编译
- 编译器是否支持C++11

### 2. 文件保存失败
可能的原因：
- 文件路径无效
- 没有写入权限
- 磁盘空间不足

### 3. 中文显示异常
检查：
- 文件是否包含UTF-8 BOM
- 编辑器是否设置为UTF-8编码
- 字体是否支持中文字符

## 扩展功能

### 1. 自定义内容模板
可以修改`SaveMermaidGraphFile`函数来自定义输出格式：

```cpp
// 在UTF8FileHelper.h中添加自定义模板函数
inline bool SaveCustomMermaidFile(const std::wstring& filePath, 
                                  const std::wstring& title,
                                  const std::wstring& mermaidGraph,
                                  const std::wstring& description)
{
    std::string content;
    content += "# " + WStringToUTF8(title) + "\n\n";
    content += "```mermaid\n";
    content += WStringToUTF8(mermaidGraph);
    content += "\n```\n\n";
    content += WStringToUTF8(description);
    
    return SaveUTF8File(filePath, content);
}
```

### 2. 批量文件处理
```cpp
// 批量保存多个图形
bool SaveMultipleMermaidGraphs(const std::vector<std::pair<std::wstring, std::wstring>>& graphs)
{
    for (const auto& graph : graphs)
    {
        if (!UTF8FileHelper::SaveMermaidGraphFile(graph.first, graph.second, 0))
        {
            return false;
        }
    }
    return true;
}
```

## 总结

UTF-8文件保存方案完美解决了中文字符编码问题，提供了：
- ✅ 完整的中文支持
- ✅ 跨平台兼容性
- ✅ 简单易用的接口
- ✅ 完善的错误处理
- ✅ 详细的文档说明

这个方案确保了CmdCalculatorGraphAnalyzer生成的文件能够在各种环境中正确显示中文内容。
