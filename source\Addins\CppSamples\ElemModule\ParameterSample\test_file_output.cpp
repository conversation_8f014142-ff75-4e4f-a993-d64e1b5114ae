// 测试文件输出功能的独立程序
// 编译命令: g++ -o test_file_output test_file_output.cpp

#include <iostream>
#include <fstream>
#include <string>
#include <vector>
#include <sstream>

#ifdef _WIN32
#include <windows.h>
#include <io.h>
#include <fcntl.h>
#endif

// 转换宽字符串为UTF-8字符串
std::string WStringToUTF8(const std::wstring& wstr)
{
#ifdef _WIN32
    if (wstr.empty()) return std::string();
    
    int len = WideCharToMultiByte(CP_UTF8, 0, wstr.c_str(), -1, nullptr, 0, nullptr, nullptr);
    if (len <= 0) return std::string();
    
    std::string result(len - 1, 0);
    WideCharToMultiByte(CP_UTF8, 0, wstr.c_str(), -1, &result[0], len, nullptr, nullptr);
    return result;
#else
    // Linux/Mac版本的转换（简化）
    std::string result;
    for (wchar_t wc : wstr) {
        if (wc < 128) {
            result += static_cast<char>(wc);
        } else {
            result += "?"; // 简化处理
        }
    }
    return result;
#endif
}

// 生成测试用的Mermaid图
std::wstring GenerateTestMermaidGraph()
{
    std::wstringstream ss;
    ss << L"graph TD\n";
    ss << L"    calc0[\"墙参数计算器<br/>(Wall:12345)\"]\n";
    ss << L"    calc1[\"墙图形计算器<br/>(Wall:12345)\"]\n";
    ss << L"    calc2[\"楼板图形计算器<br/>(Floor:67890)\"]\n";
    ss << L"    \n";
    ss << L"    calc0 --> calc1\n";
    ss << L"    calc1 --> calc2\n";
    ss << L"    \n";
    ss << L"    style calc0 fill:#e1f5fe\n";
    ss << L"    style calc1 fill:#e1f5fe\n";
    ss << L"    style calc2 fill:#f3e5f5\n";
    
    return ss.str();
}

// 方法1：UTF-8输出（推荐）
bool SaveMermaidGraphUTF8(const std::string& filePath, const std::wstring& mermaidGraph, int calculatorCount)
{
    std::ofstream outFile(filePath, std::ios::out | std::ios::binary);
    if (!outFile.is_open())
    {
        return false;
    }

    try
    {
        // 写入UTF-8 BOM
        outFile.write("\xEF\xBB\xBF", 3);
        
        // 准备内容
        std::string header = "# 计算器关联更新图\n\n";
        std::string count = "分析了 " + std::to_string(calculatorCount) + " 个计算器\n\n";
        std::string mermaidStart = "```mermaid\n";
        std::string mermaidEnd = "\n```\n\n";
        std::string footer = "## 说明\n\n这是由CmdCalculatorGraphAnalyzer生成的计算器依赖关系图。\n";
        footer += "每个节点代表一个计算器，箭头表示数据依赖关系。\n";
        
        // 转换Mermaid图为UTF-8
        std::string mermaidGraphUtf8 = WStringToUTF8(mermaidGraph);
        
        // 写入内容
        outFile << header;
        outFile << count;
        outFile << mermaidStart;
        outFile << mermaidGraphUtf8;
        outFile << mermaidEnd;
        outFile << footer;
        
        outFile.close();
        return true;
    }
    catch (...)
    {
        outFile.close();
        return false;
    }
}

// 方法2：ASCII输出（兼容性好）
bool SaveMermaidGraphASCII(const std::string& filePath, const std::wstring& mermaidGraph, int calculatorCount)
{
    std::ofstream outFile(filePath);
    if (!outFile.is_open())
    {
        return false;
    }

    try
    {
        outFile << "# Calculator Dependency Graph\n\n";
        outFile << "Analyzed " << calculatorCount << " calculators\n\n";
        outFile << "```mermaid\n";
        
        // 转换Mermaid图（简化版本，替换中文）
        std::string mermaidGraphAscii;
        for (wchar_t wc : mermaidGraph)
        {
            if (wc < 128)
            {
                mermaidGraphAscii += static_cast<char>(wc);
            }
            else
            {
                // 简单替换中文字符
                if (mermaidGraphAscii.back() != '?')
                {
                    mermaidGraphAscii += "?";
                }
            }
        }
        
        outFile << mermaidGraphAscii;
        outFile << "\n```\n\n";
        outFile << "## Description\n\n";
        outFile << "This is a calculator dependency graph generated by CmdCalculatorGraphAnalyzer.\n";
        outFile << "Each node represents a calculator, arrows show data dependencies.\n";
        
        outFile.close();
        return true;
    }
    catch (...)
    {
        outFile.close();
        return false;
    }
}

// 方法3：简化的英文版本
bool SaveMermaidGraphEnglish(const std::string& filePath, int calculatorCount)
{
    std::ofstream outFile(filePath);
    if (!outFile.is_open())
    {
        return false;
    }

    try
    {
        outFile << "# Calculator Dependency Graph\n\n";
        outFile << "Analyzed " << calculatorCount << " calculators\n\n";
        outFile << "```mermaid\n";
        outFile << "graph TD\n";
        outFile << "    calc0[\"Wall Parameter Calculator<br/>(Wall:12345)\"]\n";
        outFile << "    calc1[\"Wall Graphics Calculator<br/>(Wall:12345)\"]\n";
        outFile << "    calc2[\"Floor Graphics Calculator<br/>(Floor:67890)\"]\n";
        outFile << "    \n";
        outFile << "    calc0 --> calc1\n";
        outFile << "    calc1 --> calc2\n";
        outFile << "    \n";
        outFile << "    style calc0 fill:#e1f5fe\n";
        outFile << "    style calc1 fill:#e1f5fe\n";
        outFile << "    style calc2 fill:#f3e5f5\n";
        outFile << "\n```\n\n";
        outFile << "## Description\n\n";
        outFile << "This graph shows the dependency relationships between calculators:\n\n";
        outFile << "- **Wall Parameter Calculator**: Calculates wall parameters\n";
        outFile << "- **Wall Graphics Calculator**: Generates wall graphics based on parameters\n";
        outFile << "- **Floor Graphics Calculator**: Generates floor graphics, depends on wall graphics\n\n";
        outFile << "The arrows indicate data flow and update dependencies.\n";
        
        outFile.close();
        return true;
    }
    catch (...)
    {
        outFile.close();
        return false;
    }
}

int main()
{
#ifdef _WIN32
    // 设置控制台输出为UTF-8
    SetConsoleOutputCP(CP_UTF8);
    _setmode(_fileno(stdout), _O_U8TEXT);
#endif

    std::wcout << L"开始测试文件输出功能...\n" << std::endl;

    // 生成测试数据
    std::wstring mermaidGraph = GenerateTestMermaidGraph();
    int calculatorCount = 3;

    // 测试方法1：UTF-8输出
    std::wcout << L"测试1: UTF-8输出..." << std::endl;
    bool success1 = SaveMermaidGraphUTF8("test_output_utf8.md", mermaidGraph, calculatorCount);
    std::wcout << L"UTF-8输出结果: " << (success1 ? L"成功" : L"失败") << std::endl;

    // 测试方法2：ASCII输出
    std::wcout << L"测试2: ASCII输出..." << std::endl;
    bool success2 = SaveMermaidGraphASCII("test_output_ascii.md", mermaidGraph, calculatorCount);
    std::wcout << L"ASCII输出结果: " << (success2 ? L"成功" : L"失败") << std::endl;

    // 测试方法3：英文版本
    std::wcout << L"测试3: 英文版本输出..." << std::endl;
    bool success3 = SaveMermaidGraphEnglish("test_output_english.md", calculatorCount);
    std::wcout << L"英文版本输出结果: " << (success3 ? L"成功" : L"失败") << std::endl;

    std::wcout << L"\n测试完成！请检查生成的文件：" << std::endl;
    std::wcout << L"- test_output_utf8.md (UTF-8编码，支持中文)" << std::endl;
    std::wcout << L"- test_output_ascii.md (ASCII编码，兼容性好)" << std::endl;
    std::wcout << L"- test_output_english.md (纯英文，最佳兼容性)" << std::endl;

    return 0;
}
