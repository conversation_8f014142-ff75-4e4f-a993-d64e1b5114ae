// 测试UTF-8文件保存功能
// 编译命令: g++ -o test_utf8_save test_utf8_save.cpp -std=c++11

#include "UTF8FileHelper.h"
#include <iostream>
#include <string>

int main()
{
    // 测试数据
    std::wstring testMermaidGraph = L"graph TD\n"
        L"    calc0[\"墙参数计算器<br/>(Wall:12345)\"]\n"
        L"    calc1[\"墙图形计算器<br/>(Wall:12345)\"]\n"
        L"    calc2[\"楼板图形计算器<br/>(Floor:67890)\"]\n"
        L"    \n"
        L"    calc0 --> calc1\n"
        L"    calc1 --> calc2\n"
        L"    \n"
        L"    style calc0 fill:#e1f5fe\n"
        L"    style calc1 fill:#e1f5fe\n"
        L"    style calc2 fill:#f3e5f5\n";

    std::wstring filePath = L"test_calculator_graph.md";
    size_t calculatorCount = 3;

    std::wcout << L"开始测试UTF-8文件保存..." << std::endl;

    // 测试保存
    bool success = UTF8FileHelper::SaveMermaidGraphFile(filePath, testMermaidGraph, calculatorCount);

    if (success)
    {
        std::wcout << L"✓ 文件保存成功: " << filePath << std::endl;
        std::wcout << L"请用文本编辑器打开文件检查中文是否正确显示" << std::endl;
    }
    else
    {
        std::wcout << L"✗ 文件保存失败!" << std::endl;
    }

    // 测试基本UTF-8转换
    std::wcout << L"\n测试UTF-8转换功能..." << std::endl;
    std::wstring testString = L"测试中文字符串：墙、楼板、梁";
    std::string utf8String = UTF8FileHelper::WStringToUTF8(testString);
    
    std::wcout << L"原始字符串: " << testString << std::endl;
    std::cout << "UTF-8字符串: " << utf8String << std::endl;

    // 测试保存简单UTF-8文件
    std::string simpleContent = "# 简单测试\n\n这是一个UTF-8编码的测试文件。\n\n包含中文字符：墙、楼板、梁\n";
    bool simpleSuccess = UTF8FileHelper::SaveUTF8File(L"simple_test.txt", simpleContent);
    
    if (simpleSuccess)
    {
        std::wcout << L"✓ 简单UTF-8文件保存成功" << std::endl;
    }
    else
    {
        std::wcout << L"✗ 简单UTF-8文件保存失败" << std::endl;
    }

    std::wcout << L"\n测试完成!" << std::endl;
    return 0;
}
