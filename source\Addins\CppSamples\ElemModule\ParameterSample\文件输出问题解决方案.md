# CmdCalculatorGraphAnalyzer 文件输出问题解决方案

## 问题描述

在debug中能看到有文字内容，但是输出的md文件只有`#`标题，中文内容丢失。

## 问题原因

1. **编码问题**：`std::wofstream`在Windows上默认使用UTF-16编码，但很多文本编辑器期望UTF-8编码
2. **Locale设置**：没有正确设置locale来处理宽字符输出
3. **BOM问题**：缺少UTF-8 BOM标记导致编辑器无法正确识别编码

## 解决方案

### 方案1：使用UTF-8转换（推荐）

```cpp
// 在文件开头添加必要的头文件
#include <windows.h>

// 转换宽字符串为UTF-8字符串的函数
std::string WStringToUTF8(const std::wstring& wstr)
{
    if (wstr.empty()) return std::string();
    
    int len = WideCharToMultiByte(CP_UTF8, 0, wstr.c_str(), -1, nullptr, 0, nullptr, nullptr);
    if (len <= 0) return std::string();
    
    std::string result(len - 1, 0);
    WideCharToMultiByte(CP_UTF8, 0, wstr.c_str(), -1, &result[0], len, nullptr, nullptr);
    return result;
}

// 修改文件保存部分
void SaveMermaidGraph(const std::wstring& filePath, const std::wstring& mermaidGraph, size_t calculatorCount)
{
    // 转换文件路径为UTF-8
    std::string filePathUtf8 = WStringToUTF8(filePath);
    
    // 使用二进制模式打开文件
    std::ofstream outFile(filePathUtf8, std::ios::out | std::ios::binary);
    if (outFile.is_open())
    {
        // 写入UTF-8 BOM
        outFile.write("\xEF\xBB\xBF", 3);
        
        // 准备内容
        std::string header = "# 计算器关联更新图\n\n";
        std::string count = "分析了 " + std::to_string(calculatorCount) + " 个计算器\n\n";
        std::string mermaidStart = "```mermaid\n";
        std::string mermaidEnd = "\n```\n\n";
        std::string footer = "## 说明\n\n这是由CmdCalculatorGraphAnalyzer生成的计算器依赖关系图。\n";
        
        // 转换Mermaid图为UTF-8
        std::string mermaidGraphUtf8 = WStringToUTF8(mermaidGraph);
        
        // 写入内容
        outFile << header;
        outFile << count;
        outFile << mermaidStart;
        outFile << mermaidGraphUtf8;
        outFile << mermaidEnd;
        outFile << footer;
        
        outFile.close();
    }
}
```

### 方案2：使用ASCII字符串（简化版）

```cpp
// 避免中文字符，使用英文
void SaveMermaidGraphASCII(const std::wstring& filePath, const std::wstring& mermaidGraph, size_t calculatorCount)
{
    // 转换文件路径
    char filePathMB[MAX_PATH];
    WideCharToMultiByte(CP_ACP, 0, filePath.c_str(), -1, filePathMB, MAX_PATH, NULL, NULL);
    
    std::ofstream outFile(filePathMB);
    if (outFile.is_open())
    {
        outFile << "# Calculator Dependency Graph\n\n";
        outFile << "Analyzed " << calculatorCount << " calculators\n\n";
        outFile << "```mermaid\n";
        
        // 转换Mermaid图
        char mermaidMB[8192];
        WideCharToMultiByte(CP_ACP, 0, mermaidGraph.c_str(), -1, mermaidMB, 8192, NULL, NULL);
        outFile << mermaidMB;
        
        outFile << "\n```\n\n";
        outFile << "## Description\n\n";
        outFile << "This graph shows calculator dependencies in GDMP elements.\n";
        outFile.close();
    }
}
```

### 方案3：使用GDMP框架的文件操作

```cpp
// 使用GDMP提供的文件操作接口（如果有的话）
void SaveUsingGDMPFileAPI(const std::wstring& filePath, const std::wstring& content)
{
    // 这里需要查看GDMP是否提供了文件操作的API
    // 例如可能有类似的接口：
    // gcmp::FileUtils::WriteTextFile(filePath, content, gcmp::Encoding::UTF8);
}
```

## 调试建议

### 1. 检查内容生成

在保存文件之前，先检查生成的内容：

```cpp
// 在保存前添加调试输出
std::wcout << L"Mermaid Graph Content:\n" << mermaidGraph << std::endl;
std::wcout << L"Calculator Count: " << calculatorInfos.size() << std::endl;
```

### 2. 验证文件路径

```cpp
// 检查文件路径是否有效
if (filePathName.empty())
{
    // 处理空路径
    return;
}

std::wcout << L"Saving to: " << filePathName << std::endl;
```

### 3. 分步测试

```cpp
// 先测试简单的ASCII内容
std::ofstream testFile("test_simple.txt");
testFile << "Hello World\n";
testFile << "Calculator Count: " << calculatorInfos.size() << "\n";
testFile.close();
```

## 最终建议

1. **立即解决方案**：使用方案2（ASCII字符串），避免编码问题
2. **长期解决方案**：实现方案1（UTF-8转换），支持中文显示
3. **测试方法**：先用简单内容测试文件保存，再逐步添加复杂内容

## 示例完整代码

```cpp
// 在ExecuteCommand函数中的文件保存部分
std::wstring mermaidGraph = GenerateMermaidGraph(calculatorInfos);

// 获取保存路径
std::wstring filePathName = gcmp::UiCommonDialog::ShowSaveFileDialog(
    GBMP_TR(L"请选择导出mermaid文件"),
    L"", GBMP_TR(L"markdown文件(*.md)"));

if (!filePathName.empty())
{
    // 使用ASCII版本避免编码问题
    SaveMermaidGraphASCII(filePathName, mermaidGraph, calculatorInfos.size());
    
    // 或者使用UTF-8版本支持中文
    // SaveMermaidGraphUTF8(filePathName, mermaidGraph, calculatorInfos.size());
}
```

这样可以确保文件内容正确保存，并且可以在各种文本编辑器中正常显示。
